FROM ubuntu:25.04

# Install required packages
RUN apt-get update && \
  apt-get install -y \
  docker.io \
  curl \
  unzip \
  && rm -rf /var/lib/apt/lists/*

# Install rclone properly
RUN curl -O https://downloads.rclone.org/rclone-current-linux-amd64.zip && \
  unzip rclone-current-linux-amd64.zip && \
  cd rclone-*-linux-amd64 && \
  cp rclone /usr/bin/ && \
  chmod 755 /usr/bin/rclone && \
  cd .. && \
  rm -rf rclone-* rclone-current-linux-amd64.zip

# Create necessary directories
RUN mkdir -p /usr/local/bin/uploads

# Copy the script
COPY config_extraction.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/config_extraction.sh

# Set working directory
WORKDIR /home/<USER>

# Run the script when container starts
ENTRYPOINT ["/usr/local/bin/config_extraction.sh"]