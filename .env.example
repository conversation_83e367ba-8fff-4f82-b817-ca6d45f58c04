# Data Fragmentation Script Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# GOOGLE DRIVE CONFIGURATION
# =============================================================================

# Your rclone remote name for Google Drive
# This should match the remote name you configured with 'rclone config'
# Example: "gdrive" or "my-google-drive"
SCRIPT_GDRIVE_REMOTE=your-gdrive-remote-name

# Path to the ZIP file in Google Drive (relative to the remote root)
# Example: "data/bins.zip" or "exports/binbase-export.zip"
SCRIPT_GDRIVE_ZIP_PATH=path/to/your/source-file.zip

# Google Drive directory where processed chunks will be uploaded
# Example: "processed-data/chunks" or "binbase-fragments"
SCRIPT_GDRIVE_DIRECTORY=your-upload-directory

# =============================================================================
# LOCAL FILE CONFIGURATION
# =============================================================================

# Local directory where the ZIP file will be downloaded
# Example: "./downloads" or "/tmp/data"
SCRIPT_LOCAL_ZIP_DIRECTORY=./downloads

# Local filename for the downloaded ZIP file
# Example: "bins.zip" or "data-export.zip"
SCRIPT_LOCAL_ZIP=bins.zip

# Name of the CSV file inside the ZIP archive
# This is the file that will be extracted and processed
# Example: "bins_all.csv" or "binbase_export.csv"
SCRIPT_EXTRACTED_CSV=bins_all.csv

# =============================================================================
# PROCESSING CONFIGURATION
# =============================================================================

# Number of lines per chunk (excluding header)
# The script will split the CSV into chunks of this size
# Recommended: 10000-50000 depending on file size and processing requirements
SCRIPT_CHUNK_SIZE=10000

# Local directory where chunks will be stored temporarily
# Example: "./uploads/binbase_chunks" or "/tmp/chunks"
SCRIPT_CHUNKS_DIR=./uploads/binbase_chunks

# =============================================================================
# EXAMPLE CONFIGURATION
# =============================================================================
# Here's a complete example configuration:
#
# SCRIPT_GDRIVE_REMOTE=gdrive
# SCRIPT_GDRIVE_ZIP_PATH=exports/binbase-2025.zip
# SCRIPT_GDRIVE_DIRECTORY=processed/binbase-chunks
# SCRIPT_LOCAL_ZIP_DIRECTORY=./downloads
# SCRIPT_LOCAL_ZIP=binbase-export.zip
# SCRIPT_EXTRACTED_CSV=bins_all.csv
# SCRIPT_CHUNK_SIZE=10000
# SCRIPT_CHUNKS_DIR=./uploads/binbase_chunks

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file: cp .env.example .env
# 2. Configure rclone with Google Drive: rclone config
# 3. Fill in your actual values in the .env file
# 4. Ensure the local directories exist or will be created
# 5. Run the script: ./data_fragmentation.sh