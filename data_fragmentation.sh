#!/bin/bash

ENV_FILE=./.env

if [ -f "$ENV_FILE" ]; then
  export $(grep -v '^#' "$ENV_FILE" | xargs)
fi

GDRIVE_REMOTE="${SCRIPT_GDRIVE_REMOTE}"                             # Your rclone remote name
GDRIVE_ZIP_PATH="${SCRIPT_GDRIVE_ZIP_PATH}"                         # Path to ZIP file in Google Drive
LOCAL_ZIP_DIRECTORY="${SCRIPT_LOCAL_ZIP_DIRECTORY}"                 # Local path for downloaded ZIP
LOCAL_ZIP="${SCRIPT_LOCAL_ZIP}"                                     # Local name for downloaded ZIP
EXTRACTED_CSV="${SCRIPT_EXTRACTED_CSV}"                             # The CSV filename inside the ZIP
CHUNK_SIZE="${SCRIPT_CHUNK_SIZE}"                                   # Number of lines per chunk (excluding header)
CHUNKS_DIR="${SCRIPT_CHUNKS_DIR}"                                   # Directory to store chunks locally
GDRIVE_DIRECTORY="${SCRIPT_GDRIVE_DIRECTORY}"                       # Google Drive folder for upload

CHUNK_PREFIX="binbase_$(date +%Y-%m-%d)_"


log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

set -e

log "Starting workflow: Download, Extract, Split, Upload..."

# 1. Download ZIP file from Google Drive
log "Downloading ZIP file from Google Drive: $GDRIVE_REMOTE:$GDRIVE_ZIP_PATH"
rclone copy "$GDRIVE_REMOTE:$GDRIVE_ZIP_PATH" "$LOCAL_ZIP_DIRECTORY" --progress
log "Download complete: $LOCAL_ZIP"

# 2. Extract ZIP file
log "Extracting $LOCAL_ZIP..."
unzip -o "$LOCAL_ZIP_DIRECTORY/$LOCAL_ZIP"
log "Extraction complete."

# 3. Prepare chunks directory
log "Creating chunks directory: $CHUNKS_DIR"
rm -rf "$CHUNKS_DIR"
mkdir -p "$CHUNKS_DIR"

# 4. Split CSV into chunks (preserve header)
log "Reading header from $EXTRACTED_CSV..."
HEADER=$(head -n 1 "$EXTRACTED_CSV")
log "Splitting $EXTRACTED_CSV into $CHUNK_SIZE-line chunks..."
tail -n +2 "$EXTRACTED_CSV" | split -l $CHUNK_SIZE -d -a 3 - "$CHUNKS_DIR/${CHUNK_PREFIX}"

for chunk in $CHUNKS_DIR/$CHUNK_PREFIX*; do
    log "Adding header to chunk: $chunk"
    (echo "$HEADER"; cat "$chunk") > "${chunk}.csv"
    rm "$chunk"
done
log "Splitting complete. Created $(ls $CHUNKS_DIR/*.csv | wc -l) chunks."

# 5. Upload chunks to Google Drive
log "Uploading chunks to Google Drive folder: $GDRIVE_REMOTE:$GDRIVE_DIRECTORY"
rclone copy "$CHUNKS_DIR" "$GDRIVE_REMOTE:$GDRIVE_DIRECTORY" --progress
log "Upload complete."

log "Workflow finished successfully."
