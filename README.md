# Ryvyl Data Fragmentation Project

## Project Overview
This project is a data processing pipeline designed to handle large BIN (Bank Identification Number) datasets. It automates the process of downloading, extracting, fragmenting, and uploading financial data to cloud storage using Google Drive integration.

## Workflow Process

### 1. Data Download
- Downloads ZIP files from Google Drive using rclone
- Configurable source path via environment variables

### 2. Data Extraction
- Extracts CSV files from downloaded ZIP archives
- Preserves original data integrity

### 3. Data Fragmentation
- Splits large CSV files into manageable chunks
- Preserves CSV headers in each chunk
- Configurable chunk size (default appears to be ~10,000 records per chunk)
- Generates timestamped chunk files (format: `binbase_YYYY-MM-DD_XXX.csv`)

### 4. Cloud Upload
- Uploads processed chunks to Google Drive
- Uses rclone for reliable cloud synchronization
- Progress tracking and logging

## Dependencies

### External Services
- Google Drive for data storage and retrieval
- rclone configuration for cloud access

## Usage

### Docker Deployment
```bash
docker build -t ryvyl-data-fragmentation .
docker run -v $(pwd):/home/<USER>
```

### Direct Script Execution
```bash
chmod +x data_fragmentation.sh
./data_fragmentation.sh
```